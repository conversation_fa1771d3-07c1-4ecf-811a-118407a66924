"""
任務管理API端點
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging

from app.core.database import get_db
from app.models.analysis_task import TaskStatus, TaskPriority, TaskType
from app.services.analysis_task_service import get_analysis_task_service, AnalysisTaskService
from app.services.task_status_service import get_task_status_tracker, TaskStatusTracker
from app.services.task_scheduler import get_task_scheduler, TaskScheduler
from app.services.error_handler import ErrorHandler
from app.services.analysis_task_factory import get_analysis_task_factory

logger = logging.getLogger(__name__)

router = APIRouter()


# 請求模型
class CreateTaskRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    task_name: str = Field(..., description="任務名稱")
    task_type: str = Field(..., description="任務類型")
    description: Optional[str] = Field(None, description="任務描述")
    priority: Optional[str] = Field("normal", description="任務優先級")
    config: Optional[Dict[str, Any]] = Field(None, description="任務配置")
    scheduled_time: Optional[str] = Field(None, description="計劃執行時間")


class UpdateTaskRequest(BaseModel):
    status: Optional[str] = Field(None, description="任務狀態")
    progress: Optional[int] = Field(None, description="任務進度")
    current_step: Optional[str] = Field(None, description="當前步驟")
    priority: Optional[str] = Field(None, description="任務優先級")


class RetryTaskRequest(BaseModel):
    force: Optional[bool] = Field(False, description="是否強制重試")
    priority_boost: Optional[int] = Field(0, description="優先級提升")


class StartTaskRequest(BaseModel):
    force: Optional[bool] = Field(False, description="是否強制啟動")
    priority_boost: Optional[int] = Field(0, description="優先級提升")


class CreatePurchaseReviewRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    config: Optional[Dict[str, Any]] = Field(None, description="審查配置")


class RestartTaskRequest(BaseModel):
    force: Optional[bool] = Field(False, description="是否強制重啟")
    reset_progress: Optional[bool] = Field(True, description="是否重置進度")
    priority_boost: Optional[int] = Field(0, description="優先級提升")


# 響應模型
class TaskResponse(BaseModel):
    task_id: str
    purchase_id: str
    task_name: str
    task_type: str
    status: str
    priority: str
    progress: int
    current_step: Optional[str]
    created_time: str
    start_time: Optional[str]
    end_time: Optional[str]
    duration: Optional[int]
    error_message: Optional[str]
    can_retry: bool
    can_cancel: bool


class TaskStatisticsResponse(BaseModel):
    total_count: int
    status_counts: Dict[str, int]
    type_counts: Dict[str, int]
    priority_counts: Dict[str, int]


class TaskChainResponse(BaseModel):
    """任務鏈響應模型"""
    chain_id: str
    purchase_id: str
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    overall_status: str
    overall_progress: int
    tasks: List[TaskResponse]
    created_time: str
    estimated_completion_time: Optional[str]


class TaskDependencyResponse(BaseModel):
    """任務依賴關係響應模型"""
    task_id: str
    depends_on: List[str]
    dependents: List[str]
    can_execute: bool
    blocking_tasks: List[str]


@router.post("/tasks", response_model=TaskResponse)
async def create_task(
    request: CreateTaskRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """創建新任務"""
    
    try:
        task_service = get_analysis_task_service(db)
        
        # 驗證任務類型
        try:
            task_type = TaskType(request.task_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的任務類型: {request.task_type}")
        
        # 驗證優先級
        try:
            priority = TaskPriority(request.priority)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的優先級: {request.priority}")
        
        # 解析計劃時間
        scheduled_time = None
        if request.scheduled_time:
            from datetime import datetime
            try:
                scheduled_time = datetime.fromisoformat(request.scheduled_time)
            except ValueError:
                raise HTTPException(status_code=400, detail="無效的時間格式")
        
        # 創建任務
        task = task_service.create_task(
            purchase_id=request.purchase_id,
            task_name=request.task_name,
            task_type=task_type,
            description=request.description,
            priority=priority,
            config=request.config,
            scheduled_time=scheduled_time
        )
        
        # 調度任務
        scheduler = get_task_scheduler(db)
        await scheduler.schedule_task(task)
        
        return TaskResponse(
            task_id=task.task_id,
            purchase_id=task.purchase_id,
            task_name=task.task_name,
            task_type=task.task_type.value,
            status=task.status.value,
            priority=task.priority.value,
            progress=task.progress,
            current_step=task.current_step,
            created_time=task.created_time.isoformat(),
            start_time=task.start_time.isoformat() if task.start_time else None,
            end_time=task.end_time.isoformat() if task.end_time else None,
            duration=task.duration,
            error_message=task.error_message,
            can_retry=task.can_retry,
            can_cancel=task.can_cancel
        )
        
    except Exception as e:
        logger.error(f"創建任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: str,
    db: Session = Depends(get_db)
):
    """獲取任務詳情"""
    
    try:
        task_tracker = get_task_status_tracker(db)
        task_status = await task_tracker.get_task_status(task_id)
        
        if not task_status:
            raise HTTPException(status_code=404, detail="任務不存在")
        
        return TaskResponse(**task_status)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks", response_model=List[TaskResponse])
async def get_tasks(
    purchase_id: Optional[str] = Query(None, description="購案ID"),
    status: Optional[str] = Query(None, description="任務狀態"),
    task_type: Optional[str] = Query(None, description="任務類型"),
    priority: Optional[str] = Query(None, description="任務優先級"),
    skip: int = Query(0, description="跳過數量"),
    limit: int = Query(100, description="限制數量"),
    db: Session = Depends(get_db)
):
    """獲取任務列表"""
    
    try:
        task_service = get_analysis_task_service(db)
        
        # 驗證參數
        task_status = None
        if status:
            try:
                task_status = TaskStatus(status)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"不支持的任務狀態: {status}")
        
        task_type_enum = None
        if task_type:
            try:
                task_type_enum = TaskType(task_type)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"不支持的任務類型: {task_type}")
        
        task_priority = None
        if priority:
            try:
                task_priority = TaskPriority(priority)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"不支持的優先級: {priority}")
        
        # 獲取任務列表
        tasks = task_service.get_tasks(
            skip=skip,
            limit=limit,
            purchase_id=purchase_id,
            status=task_status,
            task_type=task_type_enum,
            priority=task_priority
        )
        
        # 轉換為響應格式
        task_responses = []
        for task in tasks:
            task_responses.append(TaskResponse(
                task_id=task.task_id,
                purchase_id=task.purchase_id,
                task_name=task.task_name,
                task_type=task.task_type.value,
                status=task.status.value,
                priority=task.priority.value,
                progress=task.progress,
                current_step=task.current_step,
                created_time=task.created_time.isoformat(),
                start_time=task.start_time.isoformat() if task.start_time else None,
                end_time=task.end_time.isoformat() if task.end_time else None,
                duration=task.duration,
                error_message=task.error_message,
                can_retry=task.can_retry,
                can_cancel=task.can_cancel
            ))
        
        return task_responses
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取任務列表失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/tasks/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: str,
    request: UpdateTaskRequest,
    db: Session = Depends(get_db)
):
    """更新任務"""
    
    try:
        task_tracker = get_task_status_tracker(db)
        
        # 驗證任務存在
        current_status = await task_tracker.get_task_status(task_id)
        if not current_status:
            raise HTTPException(status_code=404, detail="任務不存在")
        
        # 驗證狀態
        new_status = None
        if request.status:
            try:
                new_status = TaskStatus(request.status)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"不支持的任務狀態: {request.status}")
        
        # 驗證優先級
        if request.priority:
            try:
                TaskPriority(request.priority)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"不支持的優先級: {request.priority}")
        
        # 更新任務狀態
        if new_status:
            await task_tracker.update_task_status(
                task_id,
                new_status,
                progress=request.progress,
                current_step=request.current_step
            )
        elif request.progress is not None or request.current_step:
            # 只更新進度
            task_service = get_analysis_task_service(db)
            task_service.update_progress(
                task_id,
                request.progress or current_status['progress'],
                request.current_step
            )
        
        # 獲取更新後的狀態
        updated_status = await task_tracker.get_task_status(task_id)
        return TaskResponse(**updated_status)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/retry")
async def retry_task(
    task_id: str,
    request: RetryTaskRequest = RetryTaskRequest(),
    db: Session = Depends(get_db)
):
    """重試任務"""
    
    try:
        task_tracker = get_task_status_tracker(db)
        
        # 驗證任務存在
        current_status = await task_tracker.get_task_status(task_id)
        if not current_status:
            raise HTTPException(status_code=404, detail="任務不存在")
        
        # 檢查是否可以重試
        if not request.force and not current_status['can_retry']:
            raise HTTPException(status_code=400, detail="任務不能重試")
        
        # 執行重試
        success = await task_tracker.retry_failed_task(task_id)
        
        if success:
            # 重新調度任務
            task_service = get_analysis_task_service(db)
            task = task_service.get_task(task_id)
            if task:
                scheduler = get_task_scheduler(db)
                await scheduler.schedule_task(task, priority_boost=request.priority_boost)
            
            return {"message": "任務重試成功", "task_id": task_id}
        else:
            raise HTTPException(status_code=400, detail="任務重試失敗")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重試任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/cancel")
async def cancel_task(
    task_id: str,
    db: Session = Depends(get_db)
):
    """取消任務"""
    
    try:
        task_tracker = get_task_status_tracker(db)
        
        # 驗證任務存在
        current_status = await task_tracker.get_task_status(task_id)
        if not current_status:
            raise HTTPException(status_code=404, detail="任務不存在")
        
        # 檢查是否可以取消
        if not current_status['can_cancel']:
            raise HTTPException(status_code=400, detail="任務不能取消")
        
        # 執行取消
        success = await task_tracker.cancel_task(task_id)
        
        if success:
            # 從調度器中取消
            scheduler = get_task_scheduler(db)
            await scheduler.cancel_task(task_id)
            
            return {"message": "任務取消成功", "task_id": task_id}
        else:
            raise HTTPException(status_code=400, detail="任務取消失敗")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/status")
async def get_task_status(
    task_id: str,
    db: Session = Depends(get_db)
):
    """獲取任務實時狀態"""
    
    try:
        task_tracker = get_task_status_tracker(db)
        status = await task_tracker.get_task_status(task_id, use_cache=False)
        
        if not status:
            raise HTTPException(status_code=404, detail="任務不存在")
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取任務狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/errors")
async def get_task_errors(
    task_id: str,
    db: Session = Depends(get_db)
):
    """獲取任務錯誤歷史"""
    
    try:
        task_service = get_analysis_task_service(db)
        error_handler = ErrorHandler(task_service)
        
        errors = await error_handler.get_error_history(task_id)
        
        return {
            "task_id": task_id,
            "error_count": len(errors),
            "errors": [
                {
                    "error_id": error.error_id,
                    "error_type": error.error_type,
                    "error_message": error.error_message,
                    "category": error.category.value,
                    "severity": error.severity.value,
                    "occurred_at": error.occurred_at.isoformat(),
                    "retry_count": error.retry_count
                }
                for error in errors
            ]
        }
        
    except Exception as e:
        logger.error(f"獲取任務錯誤失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", response_model=TaskStatisticsResponse)
async def get_task_statistics(
    db: Session = Depends(get_db)
):
    """獲取任務統計信息"""
    
    try:
        task_service = get_analysis_task_service(db)
        stats = task_service.get_task_statistics()
        
        return TaskStatisticsResponse(**stats)
        
    except Exception as e:
        logger.error(f"獲取任務統計失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/scheduler/status")
async def get_scheduler_status(
    db: Session = Depends(get_db)
):
    """獲取調度器狀態"""
    
    try:
        scheduler = get_task_scheduler(db)
        status = await scheduler.get_scheduler_status()
        
        return status
        
    except Exception as e:
        logger.error(f"獲取調度器狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/scheduler/pause")
async def pause_scheduler(
    db: Session = Depends(get_db)
):
    """暫停調度器"""
    
    try:
        scheduler = get_task_scheduler(db)
        await scheduler.pause()
        
        return {"message": "調度器已暫停"}
        
    except Exception as e:
        logger.error(f"暫停調度器失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/scheduler/resume")
async def resume_scheduler(
    db: Session = Depends(get_db)
):
    """恢復調度器"""

    try:
        scheduler = get_task_scheduler(db)
        await scheduler.resume()

        return {"message": "調度器已恢復"}

    except Exception as e:
        logger.error(f"恢復調度器失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/scheduler/start")
async def start_scheduler(
    db: Session = Depends(get_db)
):
    """啟動調度器"""

    try:
        scheduler = get_task_scheduler(db)
        await scheduler.start()

        return {"message": "調度器已啟動"}

    except Exception as e:
        logger.error(f"啟動調度器失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/scheduler/stop")
async def stop_scheduler(
    db: Session = Depends(get_db)
):
    """停止調度器"""

    try:
        scheduler = get_task_scheduler(db)
        await scheduler.stop()

        return {"message": "調度器已停止"}

    except Exception as e:
        logger.error(f"停止調度器失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/active-tasks")
async def get_active_tasks(
    db: Session = Depends(get_db)
):
    """獲取所有活躍任務"""
    
    try:
        task_tracker = get_task_status_tracker(db)
        active_tasks = await task_tracker.get_active_tasks()
        
        return {
            "active_task_count": len(active_tasks),
            "tasks": active_tasks
        }
        
    except Exception as e:
        logger.error(f"獲取活躍任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/purchase/{purchase_id}/tasks")
async def get_purchase_tasks(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取購案的所有任務"""
    
    try:
        task_tracker = get_task_status_tracker(db)
        tasks = await task_tracker.get_purchase_tasks(purchase_id)
        
        return {
            "purchase_id": purchase_id,
            "task_count": len(tasks),
            "tasks": tasks
        }
        
    except Exception as e:
        logger.error(f"獲取購案任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/chains/{purchase_id}", response_model=List[TaskChainResponse])
async def get_task_chains(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取購案的任務鏈列表"""

    try:
        task_service = get_analysis_task_service(db)
        task_factory = get_analysis_task_factory(db)

        # 獲取購案的所有任務
        all_tasks = task_service.get_tasks(purchase_id=purchase_id, limit=1000)

        # 按任務鏈分組（通過配置中的相關信息）
        task_chains = {}

        for task in all_tasks:
            # 嘗試從配置中獲取任務鏈標識
            chain_key = None
            if task.config:
                # 如果有parse_method和file_id，認為是解析任務鏈
                if task.config.get('parse_method') and task.file_id:
                    chain_key = f"parse_{task.file_id}_{task.config.get('parse_method')}"
                # 如果有analysis_mode，認為是分析任務鏈
                elif task.config.get('analysis_mode'):
                    chain_key = f"analysis_{purchase_id}_{task.config.get('analysis_mode')}"
                # 如果有custom_chain標記，認為是自定義任務鏈
                elif task.config.get('custom_chain'):
                    chain_key = f"custom_{purchase_id}_{task.created_time.strftime('%Y%m%d_%H%M%S')}"

            # 如果無法確定任務鏈，將每個任務作為獨立鏈
            if not chain_key:
                chain_key = f"single_{task.task_id}"

            if chain_key not in task_chains:
                task_chains[chain_key] = []
            task_chains[chain_key].append(task)

        # 構建響應
        chain_responses = []
        for chain_id, chain_tasks in task_chains.items():
            # 按task_index排序
            chain_tasks.sort(key=lambda t: t.config.get('task_index', 0) if t.config else 0)

            # 統計狀態
            completed_count = sum(1 for t in chain_tasks if t.status == TaskStatus.COMPLETED)
            failed_count = sum(1 for t in chain_tasks if t.status == TaskStatus.FAILED)
            total_progress = sum(t.progress for t in chain_tasks)

            # 確定整體狀態
            if failed_count > 0:
                overall_status = "failed"
            elif completed_count == len(chain_tasks):
                overall_status = "completed"
            elif any(t.status == TaskStatus.RUNNING for t in chain_tasks):
                overall_status = "running"
            else:
                overall_status = "pending"

            # 構建任務響應
            task_responses = []
            for task in chain_tasks:
                task_responses.append(TaskResponse(
                    task_id=task.task_id,
                    purchase_id=task.purchase_id,
                    task_name=task.task_name,
                    task_type=task.task_type.value,
                    status=task.status.value,
                    priority=task.priority.value,
                    progress=task.progress,
                    current_step=task.current_step,
                    created_time=task.created_time.isoformat(),
                    start_time=task.start_time.isoformat() if task.start_time else None,
                    end_time=task.end_time.isoformat() if task.end_time else None,
                    duration=task.duration,
                    error_message=task.error_message,
                    can_retry=task.can_retry,
                    can_cancel=task.can_cancel
                ))

            chain_responses.append(TaskChainResponse(
                chain_id=chain_id,
                purchase_id=purchase_id,
                total_tasks=len(chain_tasks),
                completed_tasks=completed_count,
                failed_tasks=failed_count,
                overall_status=overall_status,
                overall_progress=total_progress // len(chain_tasks) if chain_tasks else 0,
                tasks=task_responses,
                created_time=min(t.created_time for t in chain_tasks).isoformat(),
                estimated_completion_time=None  # 可以根據需要計算
            ))

        return chain_responses

    except Exception as e:
        logger.error(f"獲取任務鏈失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/dependencies/{task_id}", response_model=TaskDependencyResponse)
async def get_task_dependencies(
    task_id: str,
    db: Session = Depends(get_db)
):
    """獲取任務依賴關係"""

    try:
        task_service = get_analysis_task_service(db)
        task = task_service.get_task(task_id)

        if not task:
            raise HTTPException(status_code=404, detail="任務不存在")

        # 獲取依賴的任務
        depends_on = task.depends_on or []

        # 查找依賴此任務的其他任務
        all_tasks = task_service.get_tasks(purchase_id=task.purchase_id, limit=1000)
        dependents = []
        for t in all_tasks:
            if t.depends_on and task_id in t.depends_on:
                dependents.append(t.task_id)

        # 檢查是否可以執行（所有依賴任務都已完成）
        can_execute = True
        blocking_tasks = []

        if depends_on:
            for dep_id in depends_on:
                dep_task = task_service.get_task(dep_id)
                if dep_task and dep_task.status != TaskStatus.COMPLETED:
                    can_execute = False
                    blocking_tasks.append(dep_id)

        return TaskDependencyResponse(
            task_id=task_id,
            depends_on=depends_on,
            dependents=dependents,
            can_execute=can_execute,
            blocking_tasks=blocking_tasks
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取任務依賴關係失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/start", response_model=TaskResponse)
async def start_task(
    task_id: str,
    request: StartTaskRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """啟動任務"""

    try:
        task_service = get_analysis_task_service(db)
        task_tracker = get_task_status_tracker(db)
        scheduler = get_task_scheduler(db)

        # 獲取任務
        task = task_service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任務不存在")

        # 檢查任務狀態
        if task.status == TaskStatus.RUNNING:
            raise HTTPException(status_code=400, detail="任務已在運行中")

        if task.status == TaskStatus.COMPLETED and not request.force:
            raise HTTPException(status_code=400, detail="任務已完成，使用 force=true 強制重新啟動")

        # 檢查依賴關係
        if task.depends_on and not request.force:
            for dep_id in task.depends_on:
                dep_task = task_service.get_task(dep_id)
                if dep_task and dep_task.status != TaskStatus.COMPLETED:
                    raise HTTPException(
                        status_code=400,
                        detail=f"依賴任務 {dep_id} 尚未完成，使用 force=true 強制啟動"
                    )

        # 重置任務狀態為 PENDING
        if task.status != TaskStatus.PENDING:
            await task_tracker.update_task_status(
                task_id,
                TaskStatus.PENDING,
                progress=0,
                current_step="準備啟動任務..."
            )

        # 調度任務
        await scheduler.schedule_task(task, priority_boost=request.priority_boost)

        # 刷新任務數據
        db.refresh(task)

        return TaskResponse(
            task_id=task.task_id,
            purchase_id=task.purchase_id,
            task_name=task.task_name,
            task_type=task.task_type.value,
            status=task.status.value,
            priority=task.priority.value,
            progress=task.progress,
            current_step=task.current_step,
            created_time=task.created_time.isoformat(),
            start_time=task.start_time.isoformat() if task.start_time else None,
            end_time=task.end_time.isoformat() if task.end_time else None,
            duration=task.duration,
            error_message=task.error_message,
            can_retry=task.can_retry,
            can_cancel=task.can_cancel
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"啟動任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/restart", response_model=TaskResponse)
async def restart_task(
    task_id: str,
    request: RestartTaskRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """重啟任務"""

    try:
        task_service = get_analysis_task_service(db)
        task_tracker = get_task_status_tracker(db)
        scheduler = get_task_scheduler(db)

        # 獲取任務
        task = task_service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任務不存在")

        # 檢查任務狀態
        if task.status == TaskStatus.PENDING and not request.force:
            raise HTTPException(status_code=400, detail="任務尚未啟動，請使用啟動功能")

        # 如果任務正在運行，先取消
        if task.status == TaskStatus.RUNNING:
            task_service.cancel_task(task_id, "重啟任務")

        # 重置任務狀態和進度
        reset_data = {
            "status": TaskStatus.PENDING,
            "current_step": "準備重啟任務...",
            "error_message": None,
            "error_details": None,
            "error_code": None,
            "start_time": None,
            "end_time": None,
            "duration": None,
            "result_data": None
        }

        if request.reset_progress:
            reset_data["progress"] = 0

        # 更新任務
        for key, value in reset_data.items():
            if hasattr(task, key):
                setattr(task, key, value)

        # 增加重試計數
        task.retry_count += 1

        db.commit()
        db.refresh(task)

        # 調度任務
        await scheduler.schedule_task(task, priority_boost=request.priority_boost)

        return TaskResponse(
            task_id=task.task_id,
            purchase_id=task.purchase_id,
            task_name=task.task_name,
            task_type=task.task_type.value,
            status=task.status.value,
            priority=task.priority.value,
            progress=task.progress,
            current_step=task.current_step,
            created_time=task.created_time.isoformat(),
            start_time=task.start_time.isoformat() if task.start_time else None,
            end_time=task.end_time.isoformat() if task.end_time else None,
            duration=task.duration,
            error_message=task.error_message,
            can_retry=task.can_retry,
            can_cancel=task.can_cancel
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重啟任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/batch/start")
async def batch_start_tasks(
    task_ids: List[str],
    force: Optional[bool] = False,
    priority_boost: Optional[int] = 0,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db)
):
    """批量啟動任務"""

    try:
        task_service = get_analysis_task_service(db)
        results = []

        for task_id in task_ids:
            try:
                # 使用內部邏輯啟動任務
                request = StartTaskRequest(force=force, priority_boost=priority_boost)
                result = await start_task(task_id, request, background_tasks, db)
                results.append({
                    "task_id": task_id,
                    "status": "success",
                    "message": "任務啟動成功"
                })
            except HTTPException as e:
                results.append({
                    "task_id": task_id,
                    "status": "error",
                    "message": e.detail
                })
            except Exception as e:
                results.append({
                    "task_id": task_id,
                    "status": "error",
                    "message": str(e)
                })

        success_count = sum(1 for r in results if r["status"] == "success")

        return {
            "total_tasks": len(task_ids),
            "success_count": success_count,
            "failed_count": len(task_ids) - success_count,
            "results": results
        }

    except Exception as e:
        logger.error(f"批量啟動任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/batch/restart")
async def batch_restart_tasks(
    task_ids: List[str],
    force: Optional[bool] = False,
    reset_progress: Optional[bool] = True,
    priority_boost: Optional[int] = 0,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db)
):
    """批量重啟任務"""

    try:
        results = []

        for task_id in task_ids:
            try:
                # 使用內部邏輯重啟任務
                request = RestartTaskRequest(
                    force=force,
                    reset_progress=reset_progress,
                    priority_boost=priority_boost
                )
                result = await restart_task(task_id, request, background_tasks, db)
                results.append({
                    "task_id": task_id,
                    "status": "success",
                    "message": "任務重啟成功"
                })
            except HTTPException as e:
                results.append({
                    "task_id": task_id,
                    "status": "error",
                    "message": e.detail
                })
            except Exception as e:
                results.append({
                    "task_id": task_id,
                    "status": "error",
                    "message": str(e)
                })

        success_count = sum(1 for r in results if r["status"] == "success")

        return {
            "total_tasks": len(task_ids),
            "success_count": success_count,
            "failed_count": len(task_ids) - success_count,
            "results": results
        }

    except Exception as e:
        logger.error(f"批量重啟任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/chains/{chain_id}/start")
async def start_task_chain(
    chain_id: str,
    force: Optional[bool] = False,
    priority_boost: Optional[int] = 0,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db)
):
    """啟動任務鏈"""

    try:
        # 解析鏈ID獲取購案ID
        if chain_id.startswith("parse_") or chain_id.startswith("analysis_") or chain_id.startswith("custom_"):
            # 從鏈ID中提取購案ID或其他標識
            parts = chain_id.split("_")
            if len(parts) >= 2:
                purchase_id = parts[1]
            else:
                raise HTTPException(status_code=400, detail="無效的鏈ID格式")
        else:
            raise HTTPException(status_code=400, detail="不支持的鏈ID格式")

        # 獲取任務鏈
        chains = await get_task_chains(purchase_id, db)
        target_chain = None

        for chain in chains:
            if chain.chain_id == chain_id:
                target_chain = chain
                break

        if not target_chain:
            raise HTTPException(status_code=404, detail="任務鏈不存在")

        # 按依賴順序啟動任務
        task_ids = [task.task_id for task in target_chain.tasks]

        # 批量啟動
        result = await batch_start_tasks(
            task_ids=task_ids,
            force=force,
            priority_boost=priority_boost,
            background_tasks=background_tasks,
            db=db
        )

        return {
            "chain_id": chain_id,
            "message": f"任務鏈啟動完成",
            "batch_result": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"啟動任務鏈失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/purchase-review/create")
async def create_purchase_review_chain(
    request: CreatePurchaseReviewRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    創建購案審查任務鏈

    創建包含15個審查要項的完整任務鏈，用於購案文件的全面審查。
    """
    try:
        logger.info(f"開始創建購案審查任務鏈: purchase_id={request.purchase_id}")

        # 獲取任務工廠
        factory = get_analysis_task_factory(db)

        # 創建購案審查任務鏈
        tasks = await factory.create_purchase_review_chain(
            purchase_id=request.purchase_id,
            config=request.config
        )

        # 構建響應
        task_list = []
        for task in tasks:
            task_list.append({
                "task_id": task.task_id,
                "task_name": task.task_name,
                "task_type": task.task_type.value,
                "description": task.description,
                "status": task.status.value,
                "priority": task.priority.value,
                "progress": task.progress,
                "estimated_duration": task.estimated_duration,
                "created_time": task.created_time.isoformat() if task.created_time else None
            })

        logger.info(f"成功創建購案審查任務鏈，共 {len(tasks)} 個任務")

        return {
            "message": "購案審查任務鏈創建成功",
            "purchase_id": request.purchase_id,
            "total_tasks": len(tasks),
            "tasks": task_list,
            "chain_id": f"purchase_review_{request.purchase_id}",
            "estimated_total_duration": sum(task.estimated_duration for task in tasks)
        }

    except Exception as e:
        logger.error(f"創建購案審查任務鏈失敗: {e}")
        import traceback
        logger.error(f"錯誤詳情: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"創建購案審查任務鏈失敗: {str(e)}")


@router.get("/purchase-review/{purchase_id}/status")
async def get_purchase_review_status(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """
    獲取購案審查任務鏈狀態

    返回指定購案的所有審查任務狀態和進度信息。
    """
    try:
        logger.info(f"獲取購案審查狀態: purchase_id={purchase_id}")

        # 獲取任務服務
        task_service = get_analysis_task_service(db)

        # 查詢購案的所有審查任務
        review_task_types = [
            TaskType.REGULATION_COMPLIANCE,
            TaskType.MAINLAND_PRODUCT_CHECK,
            TaskType.REQUIREMENT_ANALYSIS,
            TaskType.PART_NUMBER_COMPLIANCE,
            TaskType.BUDGET_ANALYSIS,
            TaskType.PROCUREMENT_SCHEDULE,
            TaskType.INSPECTION_COMPLETENESS,
            TaskType.BUDGET_CONSISTENCY,
            TaskType.MAJOR_PROCUREMENT_APPROVAL,
            TaskType.WARRANTY_TERMS,
            TaskType.PENALTY_OVERDUE,
            TaskType.PENALTY_BREACH,
            TaskType.EQUIVALENT_PRODUCT,
            TaskType.AFTER_SALES_SERVICE,
            TaskType.PRODUCT_SPECIFICATION
        ]

        all_tasks = []
        for task_type in review_task_types:
            tasks = task_service.get_tasks_by_purchase_and_type(purchase_id, task_type)
            all_tasks.extend(tasks)

        if not all_tasks:
            return {
                "purchase_id": purchase_id,
                "status": "not_found",
                "message": "未找到購案審查任務",
                "tasks": []
            }

        # 統計狀態
        status_counts = {}
        total_progress = 0
        completed_tasks = 0
        failed_tasks = 0

        task_list = []
        for task in all_tasks:
            status = task.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
            total_progress += task.progress

            if task.status == TaskStatus.COMPLETED:
                completed_tasks += 1
            elif task.status == TaskStatus.FAILED:
                failed_tasks += 1

            task_list.append({
                "task_id": task.task_id,
                "task_name": task.task_name,
                "task_type": task.task_type.value,
                "description": task.description,
                "status": status,
                "progress": task.progress,
                "current_step": task.current_step,
                "created_time": task.created_time.isoformat() if task.created_time else None,
                "updated_time": task.updated_time.isoformat() if task.updated_time else None
            })

        # 計算整體狀態
        if failed_tasks > 0:
            overall_status = "failed"
        elif completed_tasks == len(all_tasks):
            overall_status = "completed"
        elif any(task.status == TaskStatus.RUNNING for task in all_tasks):
            overall_status = "running"
        else:
            overall_status = "pending"

        return {
            "purchase_id": purchase_id,
            "status": overall_status,
            "total_tasks": len(all_tasks),
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "overall_progress": total_progress // len(all_tasks) if all_tasks else 0,
            "status_counts": status_counts,
            "tasks": task_list
        }

    except Exception as e:
        logger.error(f"獲取購案審查狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取購案審查狀態失敗: {str(e)}")
