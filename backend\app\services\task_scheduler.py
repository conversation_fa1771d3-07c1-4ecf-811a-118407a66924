"""
任務調度器服務
"""

import asyncio
import uuid
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
from sqlalchemy.orm import Session
import logging

from app.models.analysis_task import AnalysisTask, TaskStatus, TaskPriority, TaskType
from app.models.purchase import Purchase
from app.services.analysis_task_service import AnalysisTaskService
from app.services.task_status_service import get_task_status_tracker
from app.core.database import get_db

logger = logging.getLogger(__name__)


class SchedulerStatus(str, Enum):
    """調度器狀態"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    PAUSING = "pausing"
    PAUSED = "paused"
    STOPPING = "stopping"


@dataclass
class WorkerInfo:
    """工作者信息"""
    worker_id: str
    status: str  # idle, busy, error
    current_task_id: Optional[str]
    max_concurrent_tasks: int
    current_task_count: int
    last_heartbeat: datetime
    capabilities: List[str]  # 支持的任務類型
    performance_score: float  # 性能評分


@dataclass
class TaskExecution:
    """任務執行信息"""
    task_id: str
    worker_id: str
    start_time: datetime
    executor: Callable
    future: Optional[asyncio.Future]


class TaskScheduler:
    """任務調度器"""

    def __init__(self, db: Session):
        self.db = db
        self.task_service = AnalysisTaskService(db)
        self.status = SchedulerStatus.STOPPED
        
        # 調度配置
        self.max_concurrent_tasks = 10
        self.max_workers = 5
        self.task_timeout = 3600  # 1小時
        self.heartbeat_interval = 30  # 30秒
        self.cleanup_interval = 300  # 5分鐘
        
        # 工作者管理
        self.workers: Dict[str, WorkerInfo] = {}
        self.worker_queues: Dict[str, asyncio.Queue] = {}
        
        # 任務執行管理
        self.running_tasks: Dict[str, TaskExecution] = {}
        self.task_queue = asyncio.PriorityQueue()
        self._task_counter = 0  # 用於避免任務對象比較

        # 任務執行器註冊
        self.task_executors: Dict[TaskType, Callable] = {}
        
        # 調度器任務
        self.scheduler_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # 統計信息
        self.stats = {
            'total_scheduled': 0,
            'total_completed': 0,
            'total_failed': 0,
            'current_running': 0,
            'queue_size': 0,
            'average_execution_time': 0.0,
            'last_update': datetime.utcnow()
        }

    async def start(self):
        """啟動調度器"""

        if self.status != SchedulerStatus.STOPPED:
            logger.warning("調度器已經在運行")
            return

        self.status = SchedulerStatus.STARTING
        logger.info("啟動任務調度器...")

        try:
            # 註冊任務執行器
            await self._register_task_executors()

            # 初始化工作者
            await self._initialize_workers()

            # 載入待執行任務
            await self._load_pending_tasks()

            # 啟動調度器任務
            self.scheduler_task = asyncio.create_task(self._scheduler_loop())
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())

            self.status = SchedulerStatus.RUNNING
            logger.info("任務調度器已啟動")

        except Exception as e:
            self.status = SchedulerStatus.STOPPED
            logger.error(f"啟動調度器失敗: {e}")
            raise

    async def stop(self):
        """停止調度器"""
        
        if self.status == SchedulerStatus.STOPPED:
            return
        
        self.status = SchedulerStatus.STOPPING
        logger.info("停止任務調度器...")
        
        try:
            # 取消所有調度器任務
            tasks_to_cancel = [
                self.scheduler_task,
                self.heartbeat_task,
                self.cleanup_task
            ]
            
            for task in tasks_to_cancel:
                if task and not task.done():
                    task.cancel()
            
            # 等待任務完成
            await asyncio.gather(*[t for t in tasks_to_cancel if t], return_exceptions=True)
            
            # 停止所有運行中的任務
            await self._stop_running_tasks()
            
            # 清理工作者
            self.workers.clear()
            self.worker_queues.clear()
            
            self.status = SchedulerStatus.STOPPED
            logger.info("任務調度器已停止")
            
        except Exception as e:
            logger.error(f"停止調度器失敗: {e}")
            self.status = SchedulerStatus.STOPPED

    async def pause(self):
        """暫停調度器"""
        
        if self.status == SchedulerStatus.RUNNING:
            self.status = SchedulerStatus.PAUSING
            # 等待當前任務完成，但不接受新任務
            logger.info("調度器已暫停")
            self.status = SchedulerStatus.PAUSED

    async def resume(self):
        """恢復調度器"""
        
        if self.status == SchedulerStatus.PAUSED:
            self.status = SchedulerStatus.RUNNING
            logger.info("調度器已恢復")

    def register_task_executor(self, task_type: TaskType, executor: Callable):
        """註冊任務執行器"""
        
        self.task_executors[task_type] = executor
        logger.info(f"註冊任務執行器: {task_type.value}")

    async def schedule_task(self, task: AnalysisTask, priority_boost: int = 0) -> bool:
        """調度任務"""
        
        if self.status not in [SchedulerStatus.RUNNING, SchedulerStatus.PAUSED, SchedulerStatus.STARTING]:
            logger.warning(f"調度器未運行，無法調度任務: {task.task_id}")
            return False
        
        # 檢查任務執行器
        if task.task_type not in self.task_executors:
            logger.error(f"未找到任務執行器: {task.task_type.value}")
            return False
        
        # 計算優先級
        priority_score = self._calculate_priority_score(task, priority_boost)

        # 添加到隊列（使用計數器避免任務對象比較）
        self._task_counter += 1
        await self.task_queue.put((priority_score, self._task_counter, task.task_id, task))
        
        self.stats['total_scheduled'] += 1
        self.stats['queue_size'] = self.task_queue.qsize()
        
        logger.info(f"任務已調度: {task.task_id}, 優先級: {priority_score}")
        return True

    async def cancel_task(self, task_id: str) -> bool:
        """取消任務"""
        
        # 如果任務正在運行，停止執行
        if task_id in self.running_tasks:
            execution = self.running_tasks[task_id]
            if execution.future and not execution.future.done():
                execution.future.cancel()
            
            # 更新任務狀態
            task_tracker = get_task_status_tracker(self.db)
            await task_tracker.update_task_status(
                task_id,
                TaskStatus.CANCELLED,
                current_step="任務已取消"
            )
            
            del self.running_tasks[task_id]
            self.stats['current_running'] = len(self.running_tasks)
            
            logger.info(f"運行中的任務已取消: {task_id}")
            return True
        
        # 如果任務在隊列中，標記為取消
        # 注意：asyncio.PriorityQueue 不支持直接移除，所以在執行時檢查狀態
        task = self.task_service.get_task(task_id)
        if task:
            self.task_service.cancel_task(task_id)
            logger.info(f"隊列中的任務已取消: {task_id}")
            return True
        
        return False

    async def get_scheduler_status(self) -> Dict[str, Any]:
        """獲取調度器狀態"""
        
        return {
            'status': self.status.value,
            'workers': {
                worker_id: {
                    'status': worker.status,
                    'current_task_id': worker.current_task_id,
                    'current_task_count': worker.current_task_count,
                    'max_concurrent_tasks': worker.max_concurrent_tasks,
                    'capabilities': worker.capabilities,
                    'performance_score': worker.performance_score,
                    'last_heartbeat': worker.last_heartbeat.isoformat()
                }
                for worker_id, worker in self.workers.items()
            },
            'running_tasks': {
                task_id: {
                    'worker_id': execution.worker_id,
                    'start_time': execution.start_time.isoformat(),
                    'duration': (datetime.utcnow() - execution.start_time).total_seconds()
                }
                for task_id, execution in self.running_tasks.items()
            },
            'statistics': self.stats.copy(),
            'configuration': {
                'max_concurrent_tasks': self.max_concurrent_tasks,
                'max_workers': self.max_workers,
                'task_timeout': self.task_timeout
            }
        }

    async def _scheduler_loop(self):
        """調度器主循環"""
        logger.info("調度器循環已啟動")

        while self.status in [SchedulerStatus.RUNNING, SchedulerStatus.PAUSED]:
            try:
                if self.status == SchedulerStatus.PAUSED:
                    await asyncio.sleep(1)
                    continue

                # 檢查是否可以執行新任務
                if len(self.running_tasks) >= self.max_concurrent_tasks:
                    await asyncio.sleep(0.1)
                    continue

                # 從隊列獲取任務
                try:
                    priority_score, counter, task_id, task = await asyncio.wait_for(
                        self.task_queue.get(),
                        timeout=1.0
                    )
                    logger.info(f"從隊列獲取任務: {task_id}, 優先級: {priority_score}")
                except asyncio.TimeoutError:
                    # logger.debug("調度器循環：隊列為空，繼續等待")
                    continue
                
                # 檢查任務是否仍然有效
                current_task = self.task_service.get_task(task_id)
                if not current_task or current_task.status != TaskStatus.PENDING:
                    logger.debug(f"任務 {task_id} 狀態已變更，跳過執行")
                    continue

                # 分配工作者（使用最新的任務對象）
                worker_id = await self._assign_worker(current_task)
                if not worker_id:
                    # 重新放回隊列
                    self._task_counter += 1
                    await self.task_queue.put((priority_score, self._task_counter, task_id, current_task))
                    await asyncio.sleep(0.5)
                    logger.debug(f"無法分配工作者給任務 {task_id}，重新放回隊列")
                    continue

                # 執行任務（使用最新的任務對象）
                logger.info(f"分配工作者 {worker_id} 執行任務 {task_id}")
                await self._execute_task(current_task, worker_id)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"調度器循環異常: {e}")
                await asyncio.sleep(1)

    async def _heartbeat_loop(self):
        """心跳檢測循環"""
        
        while self.status != SchedulerStatus.STOPPED:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                # 檢查工作者心跳
                current_time = datetime.utcnow()
                dead_workers = []
                
                for worker_id, worker in self.workers.items():
                    # 只檢查忙碌的工作者，空閒的工作者不需要心跳檢測
                    if (worker.status == "busy" and
                        (current_time - worker.last_heartbeat).total_seconds() > self.heartbeat_interval * 3):
                        dead_workers.append(worker_id)
                    elif worker.status == "idle":
                        # 更新空閒工作者的心跳時間
                        worker.last_heartbeat = current_time
                
                # 處理死亡的工作者
                for worker_id in dead_workers:
                    await self._handle_dead_worker(worker_id)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳檢測異常: {e}")

    async def _cleanup_loop(self):
        """清理循環"""
        
        while self.status != SchedulerStatus.STOPPED:
            try:
                await asyncio.sleep(self.cleanup_interval)
                
                # 清理超時任務
                await self._cleanup_timeout_tasks()
                
                # 更新統計信息
                await self._update_statistics()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理循環異常: {e}")

    async def _initialize_workers(self):
        """初始化工作者"""
        
        for i in range(self.max_workers):
            worker_id = f"worker_{i+1}"
            
            worker = WorkerInfo(
                worker_id=worker_id,
                status="idle",
                current_task_id=None,
                max_concurrent_tasks=2,
                current_task_count=0,
                last_heartbeat=datetime.utcnow(),
                capabilities=["file_processing", "pdf_parse", "rag_build", "graph_build", "analysis"],
                performance_score=1.0
            )
            
            self.workers[worker_id] = worker
            self.worker_queues[worker_id] = asyncio.Queue()
        
        logger.info(f"初始化了 {len(self.workers)} 個工作者")

    async def _load_pending_tasks(self):
        """載入待執行任務"""
        
        pending_tasks = self.task_service.get_pending_tasks(limit=1000)
        
        for task in pending_tasks:
            await self.schedule_task(task)
        
        logger.info(f"載入了 {len(pending_tasks)} 個待執行任務")

    async def _process_pending_tasks(self):
        """處理待執行任務"""
        try:
            # 獲取所有待執行任務
            pending_tasks = self.task_service.get_tasks(status=TaskStatus.PENDING, limit=100)

            if not pending_tasks:
                logger.info("沒有待執行任務")
                return

            logger.info(f"發現 {len(pending_tasks)} 個待執行任務，開始處理...")

            # 調度所有待執行任務
            scheduled_count = 0
            for task in pending_tasks:
                try:
                    success = await self.schedule_task(task)
                    if success:
                        scheduled_count += 1
                except Exception as e:
                    logger.error(f"調度任務失敗 {task.task_id}: {e}")

            logger.info(f"成功調度 {scheduled_count}/{len(pending_tasks)} 個任務")

        except Exception as e:
            logger.error(f"處理待執行任務失敗: {e}")

    async def _register_task_executors(self):
        """註冊任務執行器"""

        try:
            from app.services.task_executors import (
                pdf_parse_executor,
                file_processing_executor,
                rag_build_executor,
                analysis_executor
            )
            from app.services.purchase_review_executors import execute_purchase_review_task

            # 註冊各種任務執行器
            self.register_task_executor(TaskType.PDF_PARSE, pdf_parse_executor)
            self.register_task_executor(TaskType.FILE_PROCESSING, file_processing_executor)
            self.register_task_executor(TaskType.RAG_BUILD, rag_build_executor)
            self.register_task_executor(TaskType.ANALYSIS, analysis_executor)

            # 為了向後兼容，也註冊其他任務類型
            self.register_task_executor(TaskType.GRAPH_BUILD, rag_build_executor)  # 使用相同的執行器
            self.register_task_executor(TaskType.EXPORT, analysis_executor)  # 使用分析執行器

            # 註冊購案審查任務執行器
            purchase_review_types = [
                TaskType.REGULATION_COMPLIANCE,
                TaskType.MAINLAND_PRODUCT_CHECK,
                TaskType.REQUIREMENT_ANALYSIS,
                TaskType.PART_NUMBER_COMPLIANCE,
                TaskType.BUDGET_ANALYSIS,
                TaskType.PROCUREMENT_SCHEDULE,
                TaskType.INSPECTION_COMPLETENESS,
                TaskType.BUDGET_CONSISTENCY,
                TaskType.MAJOR_PROCUREMENT_APPROVAL,
                TaskType.WARRANTY_TERMS,
                TaskType.PENALTY_OVERDUE,
                TaskType.PENALTY_BREACH,
                TaskType.EQUIVALENT_PRODUCT,
                TaskType.AFTER_SALES_SERVICE,
                TaskType.PRODUCT_SPECIFICATION,
            ]

            for task_type in purchase_review_types:
                self.register_task_executor(task_type, execute_purchase_review_task)

            logger.info(f"成功註冊 {len(self.task_executors)} 個任務執行器")

        except Exception as e:
            logger.error(f"註冊任務執行器失敗: {e}")
            raise

    def _calculate_priority_score(self, task: AnalysisTask, priority_boost: int = 0) -> int:
        """計算任務優先級分數（越小優先級越高）"""
        
        base_score = 1000
        
        # 優先級調整
        priority_map = {
            TaskPriority.URGENT: -300,
            TaskPriority.HIGH: -200,
            TaskPriority.NORMAL: 0,
            TaskPriority.LOW: 100
        }
        
        score = base_score + priority_map.get(task.priority, 0)
        
        # 創建時間調整（越早創建優先級越高）
        age_hours = (datetime.utcnow() - task.created_time).total_seconds() / 3600
        score -= int(age_hours)
        
        # 重試次數調整（重試次數越多優先級越低）
        score += task.retry_count * 50
        
        # 手動優先級提升
        score -= priority_boost
        
        return max(0, score)

    async def _assign_worker(self, task: AnalysisTask) -> Optional[str]:
        """分配工作者"""
        
        # 找到空閒且支持該任務類型的工作者
        available_workers = []
        
        for worker_id, worker in self.workers.items():
            if (worker.status == "idle" and 
                worker.current_task_count < worker.max_concurrent_tasks and
                task.task_type.value in worker.capabilities):
                available_workers.append((worker_id, worker))
        
        if not available_workers:
            return None
        
        # 選擇性能評分最高的工作者
        best_worker = max(available_workers, key=lambda x: x[1].performance_score)
        return best_worker[0]

    async def _execute_task(self, task: AnalysisTask, worker_id: str):
        """執行任務"""
        
        try:
            # 更新工作者狀態
            worker = self.workers[worker_id]
            worker.status = "busy"
            worker.current_task_id = task.task_id
            worker.current_task_count += 1
            worker.last_heartbeat = datetime.utcnow()  # 更新心跳時間
            
            # 獲取任務執行器
            executor = self.task_executors[task.task_type]
            
            # 創建執行記錄
            execution = TaskExecution(
                task_id=task.task_id,
                worker_id=worker_id,
                start_time=datetime.utcnow(),
                executor=executor,
                future=None
            )
            
            self.running_tasks[task.task_id] = execution
            self.stats['current_running'] = len(self.running_tasks)
            
            # 更新任務狀態
            task_tracker = get_task_status_tracker(self.db)
            await task_tracker.update_task_status(
                task.task_id,
                TaskStatus.RUNNING,
                progress=0,
                current_step="開始執行任務..."
            )
            
            # 執行任務
            execution.future = asyncio.create_task(executor(task))
            result = await execution.future
            
            # 任務完成
            await task_tracker.update_task_status(
                task.task_id,
                TaskStatus.COMPLETED,
                progress=100,
                current_step="任務執行完成",
                result_data=result
            )

            self.stats['total_completed'] += 1
            logger.info(f"任務執行完成: {task.task_id}")

            # 確保任務狀態更新已提交到數據庫
            await asyncio.sleep(0.1)  # 給數據庫一點時間提交事務

            # 檢查並調度依賴於此任務的其他任務
            await self._schedule_dependent_tasks(task.task_id)
            
        except asyncio.CancelledError:
            # 任務被取消
            logger.info(f"任務執行被取消: {task.task_id}")
        except Exception as e:
            # 任務執行失敗
            task_tracker = get_task_status_tracker(self.db)
            await task_tracker.update_task_status(
                task.task_id,
                TaskStatus.FAILED,
                current_step="任務執行失敗",
                error_message=str(e)
            )
            
            self.stats['total_failed'] += 1
            logger.error(f"任務執行失敗 {task.task_id}: {e}")
        
        finally:
            # 清理執行記錄
            if task.task_id in self.running_tasks:
                del self.running_tasks[task.task_id]
            
            # 更新工作者狀態
            if worker_id in self.workers:
                worker = self.workers[worker_id]
                worker.status = "idle"
                worker.current_task_id = None
                worker.current_task_count = max(0, worker.current_task_count - 1)
                worker.last_heartbeat = datetime.utcnow()
            
            self.stats['current_running'] = len(self.running_tasks)

    async def _stop_running_tasks(self):
        """停止所有運行中的任務"""
        
        for task_id, execution in list(self.running_tasks.items()):
            if execution.future and not execution.future.done():
                execution.future.cancel()
        
        # 等待所有任務停止
        if self.running_tasks:
            await asyncio.sleep(1)
        
        self.running_tasks.clear()

    async def _handle_dead_worker(self, worker_id: str):
        """處理死亡的工作者"""
        
        logger.warning(f"檢測到死亡工作者: {worker_id}")
        
        worker = self.workers.get(worker_id)
        if worker and worker.current_task_id:
            # 重新調度當前任務
            task = self.task_service.get_task(worker.current_task_id)
            if task and task.status == TaskStatus.RUNNING:
                await self.schedule_task(task, priority_boost=100)
        
        # 重置工作者狀態
        if worker_id in self.workers:
            worker = self.workers[worker_id]
            worker.status = "idle"
            worker.current_task_id = None
            worker.current_task_count = 0
            worker.last_heartbeat = datetime.utcnow()

    async def _cleanup_timeout_tasks(self):
        """清理超時任務"""
        
        current_time = datetime.utcnow()
        timeout_tasks = []
        
        for task_id, execution in self.running_tasks.items():
            if (current_time - execution.start_time).total_seconds() > self.task_timeout:
                timeout_tasks.append(task_id)
        
        for task_id in timeout_tasks:
            logger.warning(f"任務執行超時，強制取消: {task_id}")
            await self.cancel_task(task_id)

    async def _update_statistics(self):
        """更新統計信息"""
        
        self.stats['queue_size'] = self.task_queue.qsize()
        self.stats['last_update'] = datetime.utcnow()

        # 計算平均執行時間
        completed_tasks = self.task_service.get_tasks(
            status=TaskStatus.COMPLETED,
            limit=100
        )

        if completed_tasks:
            total_duration = sum(
                task.duration for task in completed_tasks if task.duration
            )
            self.stats['average_execution_time'] = total_duration / len(completed_tasks)

    async def _schedule_dependent_tasks(self, completed_task_id: str):
        """調度依賴於已完成任務的其他任務"""
        try:
            logger.info(f"🔍 檢查依賴任務: {completed_task_id}")

            # 查找依賴於此任務的其他任務
            dependent_tasks = self.task_service.get_tasks_by_dependency(completed_task_id)
            logger.info(f"📋 找到 {len(dependent_tasks)} 個依賴任務")

            for task in dependent_tasks:
                logger.info(f"🔍 檢查依賴任務: {task.task_id} ({task.task_name})")
                logger.info(f"📊 任務狀態: {task.status.value}, 依賴: {task.depends_on}")

                # 檢查任務是否可以執行（所有依賴都已完成）
                can_execute = await self._can_execute_task(task)
                logger.info(f"✅ 可以執行: {can_execute}")

                if can_execute:
                    logger.info(f"🚀 調度依賴任務: {task.task_id} (依賴於 {completed_task_id})")
                    success = await self.schedule_task(task)
                    logger.info(f"📊 調度結果: {success}")
                else:
                    logger.info(f"⏸️ 任務不能執行，跳過: {task.task_id}")

        except Exception as e:
            logger.error(f"調度依賴任務失敗 (完成任務: {completed_task_id}): {e}")
            import traceback
            logger.error(f"錯誤詳情: {traceback.format_exc()}")

    async def _can_execute_task(self, task) -> bool:
        """檢查任務是否可以執行（所有依賴都已完成）"""
        try:
            logger.info(f"🔍 檢查任務是否可執行: {task.task_id}")
            logger.info(f"📋 任務依賴: {task.depends_on}")

            if not task.depends_on:
                logger.info(f"✅ 無依賴，可以執行")
                return True

            # 檢查所有依賴任務是否都已完成
            for dependency_id in task.depends_on:
                logger.info(f"🔍 檢查依賴任務: {dependency_id}")

                # 使用新的數據庫會話來獲取最新狀態
                from app.core.database import get_db
                from app.models.analysis_task import AnalysisTask
                from sqlalchemy import and_

                db = next(get_db())
                try:
                    logger.info(f"🔍 使用新數據庫會話查詢依賴任務: {dependency_id}")

                    dependency_task = db.query(AnalysisTask).filter(
                        and_(
                            AnalysisTask.task_id == dependency_id,
                            AnalysisTask.is_deleted == False
                        )
                    ).first()

                    if not dependency_task:
                        logger.warning(f"❌ 依賴任務不存在: {dependency_id}")
                        return False

                    logger.info(f"📊 依賴任務狀態 (新會話): {dependency_task.status.value}")
                    logger.info(f"📊 依賴任務ID: {dependency_task.task_id}")
                    logger.info(f"📊 依賴任務名稱: {dependency_task.task_name}")

                    if dependency_task.status != TaskStatus.COMPLETED:
                        logger.info(f"⏸️ 依賴任務未完成: {dependency_id} (狀態: {dependency_task.status.value})")
                        return False
                    else:
                        logger.info(f"✅ 依賴任務已完成: {dependency_id}")
                finally:
                    db.close()

            logger.info(f"✅ 所有依賴都已完成，可以執行")
            return True

        except Exception as e:
            logger.error(f"檢查任務依賴失敗 {task.task_id}: {e}")
            import traceback
            logger.error(f"錯誤詳情: {traceback.format_exc()}")
            return False


# 全局調度器實例
_task_scheduler: Optional[TaskScheduler] = None


def get_task_scheduler(db: Session = None) -> TaskScheduler:
    """獲取任務調度器實例"""
    global _task_scheduler
    
    if _task_scheduler is None:
        if db is None:
            db = next(get_db())
        _task_scheduler = TaskScheduler(db)
    
    return _task_scheduler


async def start_task_scheduler():
    """啟動任務調度器"""
    from app.core.database import get_db

    try:
        db = next(get_db())
        scheduler = get_task_scheduler(db)

        # 確保調度器狀態正確
        if scheduler.status == SchedulerStatus.RUNNING:
            logger.info("任務調度器已經在運行")
            return

        await scheduler.start()
        logger.info("任務調度器啟動成功")

        # 檢查是否有待執行任務需要立即處理
        await scheduler._process_pending_tasks()

    except Exception as e:
        logger.error(f"啟動任務調度器失敗: {e}")
        raise


async def stop_task_scheduler():
    """停止任務調度器"""
    global _task_scheduler
    if _task_scheduler:
        await _task_scheduler.stop()
        _task_scheduler = None
